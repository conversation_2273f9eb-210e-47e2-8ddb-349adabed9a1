import 'package:get/get.dart';
import '../services/storage_service.dart';
import '../services/network_service.dart';
import '../services/media_service.dart';
import '../services/device_service.dart';
import '../services/share_service.dart';
import '../../data/services/auth_service.dart';
import '../../data/services/wallet_service.dart';
import '../../data/services/notification_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize core services synchronously to ensure they're available immediately
    Get.put<StorageService>(StorageService(), permanent: true);
    Get.putAsync<NetworkService>(() => NetworkService().init(),
        permanent: true);

    // Initialize additional core services
    Get.put<DeviceService>(DeviceService(), permanent: true);
    Get.put<MediaService>(MediaService(), permanent: true);
    Get.put<ShareService>(ShareService(), permanent: true);

    // Initialize API services
    Get.put<AuthService>(AuthService(), permanent: true);
    Get.put<WalletService>(WalletService(), permanent: true);
    Get.put<NotificationService>(NotificationService(), permanent: true);
  }
}
